{"name": "ecpair", "version": "3.0.0", "description": "Client-side Bitcoin JavaScript library ECPair", "type": "module", "main": "src/cjs/index.cjs", "module": "src/esm/index.js", "types": "src/cjs/index.d.ts", "exports": {".": {"require": "./src/cjs/index.cjs", "import": "./src/esm/index.js", "types": "./src/cjs/index.d.ts"}}, "engines": {"node": ">=20.0.0"}, "keywords": ["bitcoinjs", "bitcoin", "browserify", "javascript", "bitcoinjs"], "scripts": {"audit": "NPM_AUDIT_IGNORE_DEV=1 NPM_AUDIT_IGNORE_LEVEL=low npm-audit-whitelister .npm-audit-whitelister.json", "build": "npm run clean && tsc -p ./tsconfig.json && tsc -p tsconfig.cjs.json && npm run formatjs", "postbuild": "find src/cjs -type f -name \"*.js\" -exec bash -c 'mv \"$0\" \"${0%.js}.cjs\"' {} \\; && chmod +x ./fixup.cjs && node fixup.cjs", "clean": "rimraf src", "clean:jstests": "rimraf 'test/**/*.js'", "coverage-report": "npm run build && npm run nobuild:coverage-report", "coverage-html": "npm run build && npm run nobuild:coverage-html", "coverage": "npm run build && npm run nobuild:coverage", "format": "npm run prettier -- --write", "formatjs": "npm run prettierjs -- --write", "format:ci": "npm run prettier -- --check && npm run prettierjs -- --check", "gitdiff:ci": "npm run build && git diff --exit-code", "lint": "tslint -p tsconfig.json -c tslint.json", "lint:tests": "tslint -p test/tsconfig.json -c tslint.json", "mocha:ts": "mocha --recursive", "nobuild:coverage-report": "c8 report --reporter=lcov", "nobuild:coverage-html": "c8 report --reporter=html", "nobuild:coverage": "c8 --check-coverage --exclude='**/*.cjs' --branches 90 --functions 90 --lines 90 npm run unit", "nobuild:unit": "npm run mocha:ts -- 'test/*.ts'", "prettier": "prettier \"ts_src/**/*.ts\" \"test/**/*.ts\" --ignore-path ./.prettierignore", "prettierjs": "prettier \"src/**/*.js\" --ignore-path ./.prettierignore", "test": "npm run build && npm run format:ci && npm run lint && npm run nobuild:coverage", "unit": "npm run build && npm run nobuild:unit"}, "repository": {"type": "git", "url": "https://github.com/bitcoinjs/ecpair.git"}, "files": ["src"], "dependencies": {"uint8array-tools": "^0.0.8", "valibot": "^0.37.0", "wif": "^5.0.0"}, "devDependencies": {"@types/create-hash": "^1.2.2", "@types/mocha": "^5.2.7", "@types/node": "^20.14.2", "@types/wif": "^2.0.2", "c8": "^10.1.2", "mocha": "^10.7.3", "prettier": "^3.3.3", "rimraf": "^2.6.3", "tiny-secp256k1": "^2.2.3", "tslint": "^6.1.3", "tsx": "^4.16.5", "typescript": "^5.0.4"}, "license": "MIT"}