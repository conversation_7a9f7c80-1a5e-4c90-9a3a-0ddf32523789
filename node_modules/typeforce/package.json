{"name": "typeforce", "version": "1.18.0", "description": "Another biased type checking solution for Javascript", "keywords": ["typeforce", "types", "typechecking", "type", "exceptions", "force"], "homepage": "https://github.com/dcousens/typeforce", "bugs": {"url": "https://github.com/dcousens/typeforce/issues"}, "license": "MIT", "author": "<PERSON>", "main": "index.js", "files": ["async.js", "errors.js", "extra.js", "index.js", "native.js", "nothrow.js"], "repository": {"type": "git", "url": "https://github.com/dcousens/typeforce.git"}, "scripts": {"coverage": "nyc --check-coverage --branches 100 --functions 100 node test/*.js", "test": "npm run standard && npm run unit", "standard": "standard", "unit": "node test/*.js"}, "dependencies": {}, "devDependencies": {"nyc": "^13.1.0", "standard": "*", "tape": "^4.6.1"}}