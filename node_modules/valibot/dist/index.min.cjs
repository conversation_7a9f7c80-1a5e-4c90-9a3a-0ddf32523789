"use strict";var _=Object.defineProperty;var ss=Object.getOwnPropertyDescriptor;var us=Object.getOwnPropertyNames;var rs=Object.prototype.hasOwnProperty;var Is=(t,n)=>{for(var e in n)_(t,e,{get:n[e],enumerable:!0})},os=(t,n,e,s)=>{if(n&&typeof n=="object"||typeof n=="function")for(let u of us(n))!rs.call(t,u)&&u!==e&&_(t,u,{get:()=>n[u],enumerable:!(s=ss(n,u))||s.enumerable});return t};var is=t=>os(_({},"__esModule",{value:!0}),t);var Gs={};Is(Gs,{BASE64_REGEX:()=>q,BIC_REGEX:()=>D,CUID2_REGEX:()=>N,DECIMAL_REGEX:()=>W,EMAIL_REGEX:()=>V,EMOJI_REGEX:()=>C,HEXADECIMAL_REGEX:()=>L,HEX_COLOR_REGEX:()=>K,IMEI_REGEX:()=>$,IPV4_REGEX:()=>z,IPV6_REGEX:()=>F,IP_REGEX:()=>U,ISO_DATE_REGEX:()=>G,ISO_DATE_TIME_REGEX:()=>X,ISO_TIMESTAMP_REGEX:()=>Z,ISO_TIME_REGEX:()=>H,ISO_TIME_SECOND_REGEX:()=>J,ISO_WEEK_REGEX:()=>Q,MAC48_REGEX:()=>Y,MAC64_REGEX:()=>ee,MAC_REGEX:()=>ne,OCTAL_REGEX:()=>te,ULID_REGEX:()=>se,UUID_REGEX:()=>ue,ValiError:()=>k,_addIssue:()=>r,_isLuhnAlgo:()=>A,_isValidObjectKey:()=>y,_stringify:()=>f,any:()=>Hn,array:()=>Jn,arrayAsync:()=>Zn,awaitAsync:()=>le,base64:()=>ke,bic:()=>he,bigint:()=>Qn,blob:()=>Yn,boolean:()=>et,brand:()=>xe,bytes:()=>Oe,check:()=>we,checkAsync:()=>ge,checkItems:()=>Se,config:()=>As,creditCard:()=>Be,cuid2:()=>Ae,custom:()=>nt,customAsync:()=>tt,date:()=>st,decimal:()=>be,deleteGlobalConfig:()=>ms,deleteGlobalMessage:()=>cs,deleteSchemaMessage:()=>fs,deleteSpecificMessage:()=>ys,description:()=>Ee,email:()=>Me,emoji:()=>Pe,empty:()=>je,endsWith:()=>ve,entriesFromList:()=>ks,enum:()=>ut,enum_:()=>ut,everyItem:()=>Re,excludes:()=>_e,fallback:()=>bs,fallbackAsync:()=>Es,file:()=>rt,filterItems:()=>qe,findItem:()=>De,finite:()=>Ne,flatten:()=>Ms,forward:()=>Ps,forwardAsync:()=>js,function:()=>It,function_:()=>It,getDefault:()=>d,getDefaults:()=>ae,getDefaultsAsync:()=>me,getDotPath:()=>ie,getFallback:()=>h,getFallbacks:()=>pe,getFallbacksAsync:()=>ce,getGlobalConfig:()=>l,getGlobalMessage:()=>re,getSchemaMessage:()=>Ie,getSpecificMessage:()=>oe,hash:()=>We,hexColor:()=>Ce,hexadecimal:()=>Ve,imei:()=>Le,includes:()=>Ke,instance:()=>ot,integer:()=>$e,intersect:()=>it,intersectAsync:()=>at,ip:()=>ze,ipv4:()=>Fe,ipv6:()=>Ue,is:()=>vs,isOfKind:()=>hs,isOfType:()=>xs,isValiError:()=>Os,isoDate:()=>Ge,isoDateTime:()=>Xe,isoTime:()=>He,isoTimeSecond:()=>Je,isoTimestamp:()=>Ze,isoWeek:()=>Qe,keyof:()=>Rs,lazy:()=>mt,lazyAsync:()=>pt,length:()=>Ye,literal:()=>ct,looseObject:()=>Tt,looseObjectAsync:()=>ft,looseTuple:()=>dt,looseTupleAsync:()=>yt,mac:()=>en,mac48:()=>nn,mac64:()=>tn,map:()=>lt,mapAsync:()=>kt,mapItems:()=>sn,maxBytes:()=>un,maxLength:()=>rn,maxSize:()=>In,maxValue:()=>on,mimeType:()=>an,minBytes:()=>mn,minLength:()=>pn,minSize:()=>cn,minValue:()=>Tn,multipleOf:()=>fn,nan:()=>ht,never:()=>xt,nonEmpty:()=>dn,nonNullable:()=>Ot,nonNullableAsync:()=>wt,nonNullish:()=>gt,nonNullishAsync:()=>St,nonOptional:()=>M,nonOptionalAsync:()=>P,normalize:()=>yn,notBytes:()=>ln,notLength:()=>kn,notSize:()=>hn,notValue:()=>xn,null:()=>Bt,null_:()=>Bt,nullable:()=>At,nullableAsync:()=>bt,nullish:()=>Et,nullishAsync:()=>Mt,number:()=>Pt,object:()=>jt,objectAsync:()=>vt,objectWithRest:()=>Rt,objectWithRestAsync:()=>_t,octal:()=>On,omit:()=>_s,optional:()=>j,optionalAsync:()=>v,parse:()=>Te,parseAsync:()=>fe,parser:()=>qs,parserAsync:()=>Ds,partial:()=>Ns,partialAsync:()=>Ws,partialCheck:()=>wn,partialCheckAsync:()=>gn,pick:()=>Vs,picklist:()=>R,pipe:()=>Cs,pipeAsync:()=>Ls,promise:()=>qt,rawCheck:()=>Sn,rawCheckAsync:()=>Bn,rawTransform:()=>An,rawTransformAsync:()=>bn,readonly:()=>En,record:()=>Dt,recordAsync:()=>Nt,reduceItems:()=>Mn,regex:()=>Pn,required:()=>Ks,requiredAsync:()=>$s,safeInteger:()=>jn,safeParse:()=>de,safeParseAsync:()=>ye,safeParser:()=>zs,safeParserAsync:()=>Fs,set:()=>Wt,setAsync:()=>Vt,setGlobalConfig:()=>as,setGlobalMessage:()=>ps,setSchemaMessage:()=>Ts,setSpecificMessage:()=>ds,size:()=>vn,someItem:()=>Rn,sortItems:()=>_n,startsWith:()=>qn,strictObject:()=>Ct,strictObjectAsync:()=>Lt,strictTuple:()=>Kt,strictTupleAsync:()=>$t,string:()=>zt,symbol:()=>Ft,toLowerCase:()=>Dn,toMaxValue:()=>Nn,toMinValue:()=>Wn,toUpperCase:()=>Vn,transform:()=>Cn,transformAsync:()=>Ln,trim:()=>Kn,trimEnd:()=>$n,trimStart:()=>zn,tuple:()=>Ut,tupleAsync:()=>Gt,tupleWithRest:()=>Xt,tupleWithRestAsync:()=>Ht,ulid:()=>Fn,undefined:()=>Jt,undefined_:()=>Jt,union:()=>Zt,unionAsync:()=>Qt,unknown:()=>Yt,unwrap:()=>Us,url:()=>Un,uuid:()=>Gn,value:()=>Xn,variant:()=>es,variantAsync:()=>ns,void:()=>ts,void_:()=>ts});module.exports=is(Gs);function le(){return{kind:"transformation",type:"await",reference:le,async:!0,async _run(t){return t.value=await t.value,t}}}var q=/^(?:[\da-z+/]{4})*(?:[\da-z+/]{2}==|[\da-z+/]{3}=)?$/iu,D=/^[A-Z]{6}(?!00)[\dA-Z]{2}(?:[\dA-Z]{3})?$/u,N=/^[a-z][\da-z]*$/u,W=/^\d+$/u,V=/^[\w+-]+(?:\.[\w+-]+)*@[\da-z]+(?:[.-][\da-z]+)*\.[a-z]{2,}$/iu,C=/^(?:[\u{1F1E6}-\u{1F1FF}]{2}|\u{1F3F4}[\u{E0061}-\u{E007A}]{2}[\u{E0030}-\u{E0039}\u{E0061}-\u{E007A}]{1,3}\u{E007F}|(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation})(?:\u200D(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation}))*)+$/u,L=/^(?:0[hx])?[\da-f]+$/iu,K=/^#(?:[\da-f]{3,4}|[\da-f]{6}|[\da-f]{8})$/iu,$=/^\d{15}$|^\d{2}-\d{6}-\d{6}-\d$/u,z=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$/u,F=/^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,U=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$|^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,G=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])$/u,X=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])T(?:0\d|1\d|2[0-3]):[0-5]\d$/u,H=/^(?:0\d|1\d|2[0-3]):[0-5]\d$/u,J=/^(?:0\d|1\d|2[0-3])(?::[0-5]\d){2}$/u,Z=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])T(?:0\d|1\d|2[0-3])(?::[0-5]\d){2}(?:\.\d{1,9})?(?:Z|[+-](?:0\d|1\d|2[0-3])(?::?[0-5]\d)?)$/u,Q=/^\d{4}-W(?:0[1-9]|[1-4]\d|5[0-3])$/u,Y=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$/iu,ee=/^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,ne=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$|^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,te=/^(?:0o)?[0-7]+$/iu,se=/^[\da-hjkmnp-tv-z]{26}$/iu,ue=/^[\da-f]{8}(?:-[\da-f]{4}){3}-[\da-f]{12}$/iu;var O;function as(t){O={...O,...t}}function l(t){return{lang:t?.lang??O?.lang,message:t?.message,abortEarly:t?.abortEarly??O?.abortEarly,abortPipeEarly:t?.abortPipeEarly??O?.abortPipeEarly}}function ms(){O=void 0}var S;function ps(t,n){S||(S=new Map),S.set(n,t)}function re(t){return S?.get(t)}function cs(t){S?.delete(t)}var B;function Ts(t,n){B||(B=new Map),B.set(n,t)}function Ie(t){return B?.get(t)}function fs(t){B?.delete(t)}var x;function ds(t,n,e){x||(x=new Map),x.get(t)||x.set(t,new Map),x.get(t).set(e,n)}function oe(t,n){return x?.get(t)?.get(n)}function ys(t,n){x?.get(t)?.delete(n)}function f(t){let n=typeof t;return n==="string"?`"${t}"`:n==="number"||n==="bigint"||n==="boolean"?`${t}`:n==="object"||n==="function"?(t&&Object.getPrototypeOf(t)?.constructor?.name)??"null":n}function r(t,n,e,s,u){let o=u&&"input"in u?u.input:e.value,i=u?.expected??t.expects??null,a=u?.received??f(o),I={kind:t.kind,type:t.type,input:o,expected:i,received:a,message:`Invalid ${n}: ${i?`Expected ${i} but r`:"R"}eceived ${a}`,requirement:t.requirement,path:u?.path,issues:u?.issues,lang:s.lang,abortEarly:s.abortEarly,abortPipeEarly:s.abortPipeEarly},m=t.kind==="schema",p=u?.message??t.message??oe(t.reference,I.lang)??(m?Ie(I.lang):null)??s.message??re(I.lang);p&&(I.message=typeof p=="function"?p(I):p),m&&(e.typed=!1),e.issues?e.issues.push(I):e.issues=[I]}var ls=/\D/gu;function A(t){let n=t.replace(ls,""),e=n.length,s=1,u=0;for(;e;){let o=+n[--e];s^=1,u+=s?[0,2,4,6,8,1,3,5,7,9][o]:o}return u%10===0}function y(t,n){return Object.hasOwn(t,n)&&n!=="__proto__"&&n!=="prototype"&&n!=="constructor"}function ks(t,n){let e={};for(let s of t)e[s]=n;return e}function ie(t){if(t.path){let n="";for(let e of t.path)if(typeof e.key=="string"||typeof e.key=="number")n?n+=`.${e.key}`:n+=e.key;else return null;return n}return null}function hs(t,n){return n.kind===t}function xs(t,n){return n.type===t}function Os(t){return t instanceof k}var k=class extends Error{issues;constructor(n){super(n[0].message),this.name="ValiError",this.issues=n}};function ke(t){return{kind:"validation",type:"base64",reference:ke,async:!1,expects:null,requirement:q,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Base64",n,e),n}}}function he(t){return{kind:"validation",type:"bic",reference:he,async:!1,expects:null,requirement:D,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"BIC",n,e),n}}}function xe(t){return{kind:"transformation",type:"brand",reference:xe,async:!1,name:t,_run(n){return n}}}function Oe(t,n){return{kind:"validation",type:"bytes",reference:Oe,async:!1,expects:`${t}`,requirement:t,message:n,_run(e,s){if(e.typed){let u=new TextEncoder().encode(e.value).length;u!==this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function we(t,n){return{kind:"validation",type:"check",reference:we,async:!1,expects:null,requirement:t,message:n,_run(e,s){return e.typed&&!this.requirement(e.value)&&r(this,"input",e,s),e}}}function ge(t,n){return{kind:"validation",type:"check",reference:ge,async:!0,expects:null,requirement:t,message:n,async _run(e,s){return e.typed&&!await this.requirement(e.value)&&r(this,"input",e,s),e}}}function Se(t,n){return{kind:"validation",type:"check_items",reference:Se,async:!1,expects:null,requirement:t,message:n,_run(e,s){if(e.typed)for(let u=0;u<e.value.length;u++){let o=e.value[u];this.requirement(o,u,e.value)||r(this,"item",e,s,{input:o,path:[{type:"array",origin:"value",input:e.value,key:u,value:o}]})}return e}}}var ws=/^(?:\d{14,19}|\d{4}(?: \d{3,6}){2,4}|\d{4}(?:-\d{3,6}){2,4})$/u,gs=/[- ]/gu,Ss=[/^3[47]\d{13}$/u,/^3(?:0[0-5]|[68]\d)\d{11,13}$/u,/^6(?:011|5\d{2})\d{12,15}$/u,/^(?:2131|1800|35\d{3})\d{11}$/u,/^5[1-5]\d{2}|(?:222\d|22[3-9]\d|2[3-6]\d{2}|27[01]\d|2720)\d{12}$/u,/^(?:6[27]\d{14,17}|81\d{14,17})$/u,/^4\d{12}(?:\d{3,6})?$/u];function Be(t){return{kind:"validation",type:"credit_card",reference:Be,async:!1,expects:null,requirement(n){let e;return ws.test(n)&&(e=n.replace(gs,""))&&Ss.some(s=>s.test(e))&&A(e)},message:t,_run(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"credit card",n,e),n}}}function Ae(t){return{kind:"validation",type:"cuid2",reference:Ae,async:!1,expects:null,requirement:N,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Cuid2",n,e),n}}}function be(t){return{kind:"validation",type:"decimal",reference:be,async:!1,expects:null,requirement:W,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"decimal",n,e),n}}}function Ee(t){return{kind:"metadata",type:"description",reference:Ee,description:t}}function Me(t){return{kind:"validation",type:"email",reference:Me,expects:null,async:!1,requirement:V,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"email",n,e),n}}}function Pe(t){return{kind:"validation",type:"emoji",reference:Pe,async:!1,expects:null,requirement:C,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"emoji",n,e),n}}}function je(t){return{kind:"validation",type:"empty",reference:je,async:!1,expects:"0",message:t,_run(n,e){return n.typed&&n.value.length>0&&r(this,"length",n,e,{received:`${n.value.length}`}),n}}}function ve(t,n){return{kind:"validation",type:"ends_with",reference:ve,async:!1,expects:`"${t}"`,requirement:t,message:n,_run(e,s){return e.typed&&!e.value.endsWith(this.requirement)&&r(this,"end",e,s,{received:`"${e.value.slice(-this.requirement.length)}"`}),e}}}function Re(t,n){return{kind:"validation",type:"every_item",reference:Re,async:!1,expects:null,requirement:t,message:n,_run(e,s){return e.typed&&!e.value.every(this.requirement)&&r(this,"item",e,s),e}}}function _e(t,n){let e=f(t);return{kind:"validation",type:"excludes",reference:_e,async:!1,expects:`!${e}`,requirement:t,message:n,_run(s,u){return s.typed&&s.value.includes(this.requirement)&&r(this,"content",s,u,{received:e}),s}}}function qe(t){return{kind:"transformation",type:"filter_items",reference:qe,async:!1,operation:t,_run(n){return n.value=n.value.filter(this.operation),n}}}function De(t){return{kind:"transformation",type:"find_item",reference:De,async:!1,operation:t,_run(n){return n.value=n.value.find(this.operation),n}}}function Ne(t){return{kind:"validation",type:"finite",reference:Ne,async:!1,expects:null,requirement:Number.isFinite,message:t,_run(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"finite",n,e),n}}}var Bs={md4:32,md5:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8,adler32:8};function We(t,n){return{kind:"validation",type:"hash",reference:We,expects:null,async:!1,requirement:RegExp(t.map(e=>`^[a-f0-9]{${Bs[e]}}$`).join("|"),"iu"),message:n,_run(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"hash",e,s),e}}}function Ve(t){return{kind:"validation",type:"hexadecimal",reference:Ve,async:!1,expects:null,requirement:L,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hexadecimal",n,e),n}}}function Ce(t){return{kind:"validation",type:"hex_color",reference:Ce,async:!1,expects:null,requirement:K,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hex color",n,e),n}}}function Le(t){return{kind:"validation",type:"imei",reference:Le,async:!1,expects:null,requirement(n){return $.test(n)&&A(n)},message:t,_run(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"IMEI",n,e),n}}}function Ke(t,n){let e=f(t);return{kind:"validation",type:"includes",reference:Ke,async:!1,expects:e,requirement:t,message:n,_run(s,u){return s.typed&&!s.value.includes(this.requirement)&&r(this,"content",s,u,{received:`!${e}`}),s}}}function $e(t){return{kind:"validation",type:"integer",reference:$e,async:!1,expects:null,requirement:Number.isInteger,message:t,_run(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"integer",n,e),n}}}function ze(t){return{kind:"validation",type:"ip",reference:ze,async:!1,expects:null,requirement:U,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IP",n,e),n}}}function Fe(t){return{kind:"validation",type:"ipv4",reference:Fe,async:!1,expects:null,requirement:z,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv4",n,e),n}}}function Ue(t){return{kind:"validation",type:"ipv6",reference:Ue,async:!1,expects:null,requirement:F,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv6",n,e),n}}}function Ge(t){return{kind:"validation",type:"iso_date",reference:Ge,async:!1,expects:null,requirement:G,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date",n,e),n}}}function Xe(t){return{kind:"validation",type:"iso_date_time",reference:Xe,async:!1,expects:null,requirement:X,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date-time",n,e),n}}}function He(t){return{kind:"validation",type:"iso_time",reference:He,async:!1,expects:null,requirement:H,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time",n,e),n}}}function Je(t){return{kind:"validation",type:"iso_time_second",reference:Je,async:!1,expects:null,requirement:J,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time-second",n,e),n}}}function Ze(t){return{kind:"validation",type:"iso_timestamp",reference:Ze,async:!1,expects:null,requirement:Z,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"timestamp",n,e),n}}}function Qe(t){return{kind:"validation",type:"iso_week",reference:Qe,async:!1,expects:null,requirement:Q,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"week",n,e),n}}}function Ye(t,n){return{kind:"validation",type:"length",reference:Ye,async:!1,expects:`${t}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value.length!==this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function en(t){return{kind:"validation",type:"mac",reference:en,async:!1,expects:null,requirement:ne,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"MAC",n,e),n}}}function nn(t){return{kind:"validation",type:"mac48",reference:nn,async:!1,expects:null,requirement:Y,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"48-bit MAC",n,e),n}}}function tn(t){return{kind:"validation",type:"mac64",reference:tn,async:!1,expects:null,requirement:ee,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"64-bit MAC",n,e),n}}}function sn(t){return{kind:"transformation",type:"map_items",reference:sn,async:!1,operation:t,_run(n){return n.value=n.value.map(this.operation),n}}}function un(t,n){return{kind:"validation",type:"max_bytes",reference:un,async:!1,expects:`<=${t}`,requirement:t,message:n,_run(e,s){if(e.typed){let u=new TextEncoder().encode(e.value).length;u>this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function rn(t,n){return{kind:"validation",type:"max_length",reference:rn,async:!1,expects:`<=${t}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value.length>this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function In(t,n){return{kind:"validation",type:"max_size",reference:In,async:!1,expects:`<=${t}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value.size>this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function on(t,n){return{kind:"validation",type:"max_value",reference:on,async:!1,expects:`<=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value>this.requirement&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function an(t,n){return{kind:"validation",type:"mime_type",reference:an,async:!1,expects:t.map(e=>`"${e}"`).join(" | ")||"never",requirement:t,message:n,_run(e,s){return e.typed&&!this.requirement.includes(e.value.type)&&r(this,"MIME type",e,s,{received:`"${e.value.type}"`}),e}}}function mn(t,n){return{kind:"validation",type:"min_bytes",reference:mn,async:!1,expects:`>=${t}`,requirement:t,message:n,_run(e,s){if(e.typed){let u=new TextEncoder().encode(e.value).length;u<this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function pn(t,n){return{kind:"validation",type:"min_length",reference:pn,async:!1,expects:`>=${t}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value.length<this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function cn(t,n){return{kind:"validation",type:"min_size",reference:cn,async:!1,expects:`>=${t}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value.size<this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Tn(t,n){return{kind:"validation",type:"min_value",reference:Tn,async:!1,expects:`>=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value<this.requirement&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function fn(t,n){return{kind:"validation",type:"multiple_of",reference:fn,async:!1,expects:`%${t}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value%this.requirement!==0&&r(this,"multiple",e,s),e}}}function dn(t){return{kind:"validation",type:"non_empty",reference:dn,async:!1,expects:"!0",message:t,_run(n,e){return n.typed&&n.value.length===0&&r(this,"length",n,e,{received:"0"}),n}}}function yn(t){return{kind:"transformation",type:"normalize",reference:yn,async:!1,form:t,_run(n){return n.value=n.value.normalize(this.form),n}}}function ln(t,n){return{kind:"validation",type:"not_bytes",reference:ln,async:!1,expects:`!${t}`,requirement:t,message:n,_run(e,s){if(e.typed){let u=new TextEncoder().encode(e.value).length;u===this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function kn(t,n){return{kind:"validation",type:"not_length",reference:kn,async:!1,expects:`!${t}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value.length===this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function hn(t,n){return{kind:"validation",type:"not_size",reference:hn,async:!1,expects:`!${t}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value.size===this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function xn(t,n){return{kind:"validation",type:"not_value",reference:xn,async:!1,expects:t instanceof Date?`!${t.toJSON()}`:`!${f(t)}`,requirement:t,message:n,_run(e,s){return e.typed&&this.requirement<=e.value&&this.requirement>=e.value&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function On(t){return{kind:"validation",type:"octal",reference:On,async:!1,expects:null,requirement:te,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"octal",n,e),n}}}function E(t,n){if(t.issues)for(let e of n)for(let s of t.issues){let u=!1,o=Math.min(e.length,s.path?.length??0);for(let i=0;i<o;i++)if(e[i]!==s.path[i].key){u=!0;break}if(!u)return!1}return!0}function wn(t,n,e){return{kind:"validation",type:"partial_check",reference:wn,async:!1,expects:null,requirement:n,message:e,_run(s,u){return E(s,t)&&!this.requirement(s.value)&&r(this,"input",s,u),s}}}function gn(t,n,e){return{kind:"validation",type:"partial_check",reference:gn,async:!0,expects:null,requirement:n,message:e,async _run(s,u){return E(s,t)&&!await this.requirement(s.value)&&r(this,"input",s,u),s}}}function Sn(t){return{kind:"validation",type:"raw_check",reference:Sn,async:!1,expects:null,_run(n,e){return t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function Bn(t){return{kind:"validation",type:"raw_check",reference:Bn,async:!0,expects:null,async _run(n,e){return await t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function An(t){return{kind:"transformation",type:"raw_transform",reference:An,async:!1,_run(n,e){let s=t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function bn(t){return{kind:"transformation",type:"raw_transform",reference:bn,async:!0,async _run(n,e){let s=await t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function En(){return{kind:"transformation",type:"readonly",reference:En,async:!1,_run(t){return t}}}function Mn(t,n){return{kind:"transformation",type:"reduce_items",reference:Mn,async:!1,operation:t,initial:n,_run(e){return e.value=e.value.reduce(this.operation,this.initial),e}}}function Pn(t,n){return{kind:"validation",type:"regex",reference:Pn,async:!1,expects:`${t}`,requirement:t,message:n,_run(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"format",e,s),e}}}function jn(t){return{kind:"validation",type:"safe_integer",reference:jn,async:!1,expects:null,requirement:Number.isSafeInteger,message:t,_run(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"safe integer",n,e),n}}}function vn(t,n){return{kind:"validation",type:"size",reference:vn,async:!1,expects:`${t}`,requirement:t,message:n,_run(e,s){return e.typed&&e.value.size!==this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Rn(t,n){return{kind:"validation",type:"some_item",reference:Rn,async:!1,expects:null,requirement:t,message:n,_run(e,s){return e.typed&&!e.value.some(this.requirement)&&r(this,"item",e,s),e}}}function _n(t){return{kind:"transformation",type:"sort_items",reference:_n,async:!1,operation:t,_run(n){return n.value=n.value.sort(this.operation),n}}}function qn(t,n){return{kind:"validation",type:"starts_with",reference:qn,async:!1,expects:`"${t}"`,requirement:t,message:n,_run(e,s){return e.typed&&!e.value.startsWith(this.requirement)&&r(this,"start",e,s,{received:`"${e.value.slice(0,this.requirement.length)}"`}),e}}}function Dn(){return{kind:"transformation",type:"to_lower_case",reference:Dn,async:!1,_run(t){return t.value=t.value.toLowerCase(),t}}}function Nn(t){return{kind:"transformation",type:"to_max_value",reference:Nn,async:!1,requirement:t,_run(n){return n.value=n.value>this.requirement?this.requirement:n.value,n}}}function Wn(t){return{kind:"transformation",type:"to_min_value",reference:Wn,async:!1,requirement:t,_run(n){return n.value=n.value<this.requirement?this.requirement:n.value,n}}}function Vn(){return{kind:"transformation",type:"to_upper_case",reference:Vn,async:!1,_run(t){return t.value=t.value.toUpperCase(),t}}}function Cn(t){return{kind:"transformation",type:"transform",reference:Cn,async:!1,operation:t,_run(n){return n.value=this.operation(n.value),n}}}function Ln(t){return{kind:"transformation",type:"transform",reference:Ln,async:!0,operation:t,async _run(n){return n.value=await this.operation(n.value),n}}}function Kn(){return{kind:"transformation",type:"trim",reference:Kn,async:!1,_run(t){return t.value=t.value.trim(),t}}}function $n(){return{kind:"transformation",type:"trim_end",reference:$n,async:!1,_run(t){return t.value=t.value.trimEnd(),t}}}function zn(){return{kind:"transformation",type:"trim_start",reference:zn,async:!1,_run(t){return t.value=t.value.trimStart(),t}}}function Fn(t){return{kind:"validation",type:"ulid",reference:Fn,async:!1,expects:null,requirement:se,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"ULID",n,e),n}}}function Un(t){return{kind:"validation",type:"url",reference:Un,async:!1,expects:null,requirement(n){try{return new URL(n),!0}catch{return!1}},message:t,_run(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"URL",n,e),n}}}function Gn(t){return{kind:"validation",type:"uuid",reference:Gn,async:!1,expects:null,requirement:ue,message:t,_run(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"UUID",n,e),n}}}function Xn(t,n){return{kind:"validation",type:"value",reference:Xn,async:!1,expects:t instanceof Date?t.toJSON():f(t),requirement:t,message:n,_run(e,s){return e.typed&&!(this.requirement<=e.value&&this.requirement>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function As(t,n){return{...t,_run(e,s){return t._run(e,{...s,...n})}}}function h(t,n,e){return typeof t.fallback=="function"?t.fallback(n,e):t.fallback}function bs(t,n){return{...t,fallback:n,_run(e,s){let u=t._run(e,s);return u.issues?{typed:!0,value:h(this,u,s)}:u}}}function Es(t,n){return{...t,fallback:n,async:!0,async _run(e,s){let u=await t._run(e,s);return u.issues?{typed:!0,value:await h(this,u,s)}:u}}}function Ms(t){let n={};for(let e of t)if(e.path){let s=ie(e);s?(n.nested||(n.nested={}),n.nested[s]?n.nested[s].push(e.message):n.nested[s]=[e.message]):n.other?n.other.push(e.message):n.other=[e.message]}else n.root?n.root.push(e.message):n.root=[e.message];return n}function Ps(t,n){return{...t,_run(e,s){let u=e.issues&&[...e.issues];if(t._run(e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let i=e.value;for(let a of n){let I=i[a],m={type:"unknown",origin:"value",input:i,key:a,value:I};if(o.path?o.path.push(m):o.path=[m],!I)break;i=I}}}return e}}}function js(t,n){return{...t,async:!0,async _run(e,s){let u=e.issues&&[...e.issues];if(await t._run(e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let i=e.value;for(let a of n){let I=i[a],m={type:"unknown",origin:"value",input:i,key:a,value:I};if(o.path?o.path.push(m):o.path=[m],!I)break;i=I}}}return e}}}function d(t,n,e){return typeof t.default=="function"?t.default(n,e):t.default}function ae(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=ae(t.entries[e]);return n}return"items"in t?t.items.map(ae):d(t)}async function me(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await me(e)]))):"items"in t?Promise.all(t.items.map(me)):d(t)}function pe(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=pe(t.entries[e]);return n}return"items"in t?t.items.map(pe):h(t)}async function ce(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await ce(e)]))):"items"in t?Promise.all(t.items.map(ce)):h(t)}function vs(t,n){return!t._run({typed:!1,value:n},{abortEarly:!0}).issues}function Hn(){return{kind:"schema",type:"any",reference:Hn,expects:"any",async:!1,_run(t){return t.typed=!0,t}}}function Jn(t,n){return{kind:"schema",type:"array",reference:Jn,expects:"Array",async:!1,item:t,message:n,_run(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<u.length;o++){let i=u[o],a=this.item._run({typed:!1,value:i},s);if(a.issues){let I={type:"array",origin:"value",input:u,key:o,value:i};for(let m of a.issues)m.path?m.path.unshift(I):m.path=[I],e.issues?.push(m);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.push(a.value)}}else r(this,"type",e,s);return e}}}function Zn(t,n){return{kind:"schema",type:"array",reference:Zn,expects:"Array",async:!0,item:t,message:n,async _run(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(u.map(i=>this.item._run({typed:!1,value:i},s)));for(let i=0;i<o.length;i++){let a=o[i];if(a.issues){let I={type:"array",origin:"value",input:u,key:i,value:u[i]};for(let m of a.issues)m.path?m.path.unshift(I):m.path=[I],e.issues?.push(m);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.push(a.value)}}else r(this,"type",e,s);return e}}}function Qn(t){return{kind:"schema",type:"bigint",reference:Qn,expects:"bigint",async:!1,message:t,_run(n,e){return typeof n.value=="bigint"?n.typed=!0:r(this,"type",n,e),n}}}function Yn(t){return{kind:"schema",type:"blob",reference:Yn,expects:"Blob",async:!1,message:t,_run(n,e){return n.value instanceof Blob?n.typed=!0:r(this,"type",n,e),n}}}function et(t){return{kind:"schema",type:"boolean",reference:et,expects:"boolean",async:!1,message:t,_run(n,e){return typeof n.value=="boolean"?n.typed=!0:r(this,"type",n,e),n}}}function nt(t,n){return{kind:"schema",type:"custom",reference:nt,expects:"unknown",async:!1,check:t,message:n,_run(e,s){return this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function tt(t,n){return{kind:"schema",type:"custom",reference:tt,expects:"unknown",async:!0,check:t,message:n,async _run(e,s){return await this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function st(t){return{kind:"schema",type:"date",reference:st,expects:"Date",async:!1,message:t,_run(n,e){return n.value instanceof Date?isNaN(n.value)?r(this,"type",n,e,{received:'"Invalid Date"'}):n.typed=!0:r(this,"type",n,e),n}}}function ut(t,n){let e=Object.entries(t).filter(([s])=>isNaN(+s)).map(([,s])=>s);return{kind:"schema",type:"enum",reference:ut,expects:e.map(f).join(" | ")||"never",async:!1,enum:t,options:e,message:n,_run(s,u){return this.options.includes(s.value)?s.typed=!0:r(this,"type",s,u),s}}}function rt(t){return{kind:"schema",type:"file",reference:rt,expects:"File",async:!1,message:t,_run(n,e){return n.value instanceof File?n.typed=!0:r(this,"type",n,e),n}}}function It(t){return{kind:"schema",type:"function",reference:It,expects:"Function",async:!1,message:t,_run(n,e){return typeof n.value=="function"?n.typed=!0:r(this,"type",n,e),n}}}function ot(t,n){return{kind:"schema",type:"instance",reference:ot,expects:t.name,async:!1,class:t,message:n,_run(e,s){return e.value instanceof this.class?e.typed=!0:r(this,"type",e,s),e}}}function w(t,n){if(typeof t==typeof n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{value:t};if(t&&n&&t.constructor===Object&&n.constructor===Object){for(let e in n)if(e in t){let s=w(t[e],n[e]);if(s.issue)return s;t[e]=s.value}else t[e]=n[e];return{value:t}}if(Array.isArray(t)&&Array.isArray(n)&&t.length===n.length){for(let e=0;e<t.length;e++){let s=w(t[e],n[e]);if(s.issue)return s;t[e]=s.value}return{value:t}}}return{issue:!0}}function it(t,n){return{kind:"schema",type:"intersect",reference:it,expects:[...new Set(t.map(e=>e.expects))].join(" & ")||"never",async:!1,options:t,message:n,_run(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;for(let i of this.options){let a=i._run({typed:!1,value:u},s);if(a.issues&&(e.issues?e.issues.push(...a.issues):e.issues=a.issues,s.abortEarly)){e.typed=!1;break}a.typed||(e.typed=!1),e.typed&&(o?o.push(a.value):o=[a.value])}if(e.typed){e.value=o[0];for(let i=1;i<o.length;i++){let a=w(e.value,o[i]);if(a.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=a.value}}}else r(this,"type",e,s);return e}}}function at(t,n){return{kind:"schema",type:"intersect",reference:at,expects:[...new Set(t.map(e=>e.expects))].join(" & ")||"never",async:!0,options:t,message:n,async _run(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;let i=await Promise.all(this.options.map(a=>a._run({typed:!1,value:u},s)));for(let a of i){if(a.issues&&(e.issues?e.issues.push(...a.issues):e.issues=a.issues,s.abortEarly)){e.typed=!1;break}a.typed||(e.typed=!1),e.typed&&(o?o.push(a.value):o=[a.value])}if(e.typed){e.value=o[0];for(let a=1;a<o.length;a++){let I=w(e.value,o[a]);if(I.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=I.value}}}else r(this,"type",e,s);return e}}}function mt(t){return{kind:"schema",type:"lazy",reference:mt,expects:"unknown",async:!1,getter:t,_run(n,e){return this.getter(n.value)._run(n,e)}}}function pt(t){return{kind:"schema",type:"lazy",reference:pt,expects:"unknown",async:!0,getter:t,async _run(n,e){return(await this.getter(n.value))._run(n,e)}}}function ct(t,n){return{kind:"schema",type:"literal",reference:ct,expects:f(t),async:!1,literal:t,message:n,_run(e,s){return e.value===this.literal?e.typed=!0:r(this,"type",e,s),e}}}function Tt(t,n){return{kind:"schema",type:"loose_object",reference:Tt,expects:"Object",async:!1,entries:t,message:n,_run(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let i=u[o],a=this.entries[o]._run({typed:!1,value:i},s);if(a.issues){let I={type:"object",origin:"value",input:u,key:o,value:i};for(let m of a.issues)m.path?m.path.unshift(I):m.path=[I],e.issues?.push(m);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),(a.value!==void 0||o in u)&&(e.value[o]=a.value)}if(!e.issues||!s.abortEarly)for(let o in u)y(u,o)&&!(o in this.entries)&&(e.value[o]=u[o])}else r(this,"type",e,s);return e}}}function ft(t,n){return{kind:"schema",type:"loose_object",reference:ft,expects:"Object",async:!0,entries:t,message:n,async _run(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([i,a])=>{let I=u[i];return[i,I,await a._run({typed:!1,value:I},s)]}));for(let[i,a,I]of o){if(I.issues){let m={type:"object",origin:"value",input:u,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],e.issues?.push(p);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),(I.value!==void 0||i in u)&&(e.value[i]=I.value)}if(!e.issues||!s.abortEarly)for(let i in u)y(u,i)&&!(i in this.entries)&&(e.value[i]=u[i])}else r(this,"type",e,s);return e}}}function dt(t,n){return{kind:"schema",type:"loose_tuple",reference:dt,expects:"Array",async:!1,items:t,message:n,_run(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let i=u[o],a=this.items[o]._run({typed:!1,value:i},s);if(a.issues){let I={type:"array",origin:"value",input:u,key:o,value:i};for(let m of a.issues)m.path?m.path.unshift(I):m.path=[I],e.issues?.push(m);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.push(a.value)}if(!e.issues||!s.abortEarly)for(let o=this.items.length;o<u.length;o++)e.value.push(u[o])}else r(this,"type",e,s);return e}}}function yt(t,n){return{kind:"schema",type:"loose_tuple",reference:yt,expects:"Array",async:!0,items:t,message:n,async _run(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(i,a)=>{let I=u[a];return[a,I,await i._run({typed:!1,value:I},s)]}));for(let[i,a,I]of o){if(I.issues){let m={type:"array",origin:"value",input:u,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],e.issues?.push(p);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}if(!e.issues||!s.abortEarly)for(let i=this.items.length;i<u.length;i++)e.value.push(u[i])}else r(this,"type",e,s);return e}}}function lt(t,n,e){return{kind:"schema",type:"map",reference:lt,expects:"Map",async:!1,key:t,value:n,message:e,_run(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;for(let[i,a]of o){let I=this.key._run({typed:!1,value:i},u);if(I.issues){let p={type:"map",origin:"key",input:o,key:i,value:a};for(let c of I.issues)c.path?c.path.unshift(p):c.path=[p],s.issues?.push(c);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}let m=this.value._run({typed:!1,value:a},u);if(m.issues){let p={type:"map",origin:"value",input:o,key:i,value:a};for(let c of m.issues)c.path?c.path.unshift(p):c.path=[p],s.issues?.push(c);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!I.typed||!m.typed)&&(s.typed=!1),s.value.set(I.value,m.value)}}else r(this,"type",s,u);return s}}}function kt(t,n,e){return{kind:"schema",type:"map",reference:kt,expects:"Map",async:!0,key:t,value:n,message:e,async _run(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;let i=await Promise.all([...o].map(([a,I])=>Promise.all([a,I,this.key._run({typed:!1,value:a},u),this.value._run({typed:!1,value:I},u)])));for(let[a,I,m,p]of i){if(m.issues){let c={type:"map",origin:"key",input:o,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(p.issues){let c={type:"map",origin:"value",input:o,key:a,value:I};for(let T of p.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=p.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!p.typed)&&(s.typed=!1),s.value.set(m.value,p.value)}}else r(this,"type",s,u);return s}}}function ht(t){return{kind:"schema",type:"nan",reference:ht,expects:"NaN",async:!1,message:t,_run(n,e){return Number.isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function xt(t){return{kind:"schema",type:"never",reference:xt,expects:"never",async:!1,message:t,_run(n,e){return r(this,"type",n,e),n}}}function Ot(t,n){return{kind:"schema",type:"non_nullable",reference:Ot,expects:"!null",async:!1,wrapped:t,message:n,_run(e,s){return e.value===null?(r(this,"type",e,s),e):this.wrapped._run(e,s)}}}function wt(t,n){return{kind:"schema",type:"non_nullable",reference:wt,expects:"!null",async:!0,wrapped:t,message:n,async _run(e,s){return e.value===null?(r(this,"type",e,s),e):this.wrapped._run(e,s)}}}function gt(t,n){return{kind:"schema",type:"non_nullish",reference:gt,expects:"!null & !undefined",async:!1,wrapped:t,message:n,_run(e,s){return e.value===null||e.value===void 0?(r(this,"type",e,s),e):this.wrapped._run(e,s)}}}function St(t,n){return{kind:"schema",type:"non_nullish",reference:St,expects:"!null & !undefined",async:!0,wrapped:t,message:n,async _run(e,s){return e.value===null||e.value===void 0?(r(this,"type",e,s),e):this.wrapped._run(e,s)}}}function M(t,n){return{kind:"schema",type:"non_optional",reference:M,expects:"!undefined",async:!1,wrapped:t,message:n,_run(e,s){return e.value===void 0?(r(this,"type",e,s),e):this.wrapped._run(e,s)}}}function P(t,n){return{kind:"schema",type:"non_optional",reference:P,expects:"!undefined",async:!0,wrapped:t,message:n,async _run(e,s){return e.value===void 0?(r(this,"type",e,s),e):this.wrapped._run(e,s)}}}function Bt(t){return{kind:"schema",type:"null",reference:Bt,expects:"null",async:!1,message:t,_run(n,e){return n.value===null?n.typed=!0:r(this,"type",n,e),n}}}function At(t,...n){let e={kind:"schema",type:"nullable",reference:At,expects:`${t.expects} | null`,async:!1,wrapped:t,_run(s,u){return s.value===null&&("default"in this&&(s.value=d(this,s,u)),s.value===null)?(s.typed=!0,s):this.wrapped._run(s,u)}};return 0 in n&&(e.default=n[0]),e}function bt(t,...n){let e={kind:"schema",type:"nullable",reference:bt,expects:`${t.expects} | null`,async:!0,wrapped:t,async _run(s,u){return s.value===null&&("default"in this&&(s.value=await d(this,s,u)),s.value===null)?(s.typed=!0,s):this.wrapped._run(s,u)}};return 0 in n&&(e.default=n[0]),e}function Et(t,...n){let e={kind:"schema",type:"nullish",reference:Et,expects:`${t.expects} | null | undefined`,async:!1,wrapped:t,_run(s,u){return(s.value===null||s.value===void 0)&&("default"in this&&(s.value=d(this,s,u)),s.value===null||s.value===void 0)?(s.typed=!0,s):this.wrapped._run(s,u)}};return 0 in n&&(e.default=n[0]),e}function Mt(t,...n){let e={kind:"schema",type:"nullish",reference:Mt,expects:`${t.expects} | null | undefined`,async:!0,wrapped:t,async _run(s,u){return(s.value===null||s.value===void 0)&&("default"in this&&(s.value=await d(this,s,u)),s.value===null||s.value===void 0)?(s.typed=!0,s):this.wrapped._run(s,u)}};return 0 in n&&(e.default=n[0]),e}function Pt(t){return{kind:"schema",type:"number",reference:Pt,expects:"number",async:!1,message:t,_run(n,e){return typeof n.value=="number"&&!isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function jt(t,n){return{kind:"schema",type:"object",reference:jt,expects:"Object",async:!1,entries:t,message:n,_run(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let i=u[o],a=this.entries[o]._run({typed:!1,value:i},s);if(a.issues){let I={type:"object",origin:"value",input:u,key:o,value:i};for(let m of a.issues)m.path?m.path.unshift(I):m.path=[I],e.issues?.push(m);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),(a.value!==void 0||o in u)&&(e.value[o]=a.value)}}else r(this,"type",e,s);return e}}}function vt(t,n){return{kind:"schema",type:"object",reference:vt,expects:"Object",async:!0,entries:t,message:n,async _run(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([i,a])=>{let I=u[i];return[i,I,await a._run({typed:!1,value:I},s)]}));for(let[i,a,I]of o){if(I.issues){let m={type:"object",origin:"value",input:u,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],e.issues?.push(p);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),(I.value!==void 0||i in u)&&(e.value[i]=I.value)}}else r(this,"type",e,s);return e}}}function Rt(t,n,e){return{kind:"schema",type:"object_with_rest",reference:Rt,expects:"Object",async:!1,entries:t,rest:n,message:e,_run(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let i in this.entries){let a=o[i],I=this.entries[i]._run({typed:!1,value:a},u);if(I.issues){let m={type:"object",origin:"value",input:o,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],s.issues?.push(p);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}I.typed||(s.typed=!1),(I.value!==void 0||i in o)&&(s.value[i]=I.value)}if(!s.issues||!u.abortEarly){for(let i in o)if(y(o,i)&&!(i in this.entries)){let a=o[i],I=this.rest._run({typed:!1,value:a},u);if(I.issues){let m={type:"object",origin:"value",input:o,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],s.issues?.push(p);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}I.typed||(s.typed=!1),s.value[i]=I.value}}}else r(this,"type",s,u);return s}}}function _t(t,n,e){return{kind:"schema",type:"object_with_rest",reference:_t,expects:"Object",async:!0,entries:t,rest:n,message:e,async _run(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let[i,a]=await Promise.all([Promise.all(Object.entries(this.entries).map(async([I,m])=>{let p=o[I];return[I,p,await m._run({typed:!1,value:p},u)]})),Promise.all(Object.entries(o).filter(([I])=>y(o,I)&&!(I in this.entries)).map(async([I,m])=>[I,m,await this.rest._run({typed:!1,value:m},u)]))]);for(let[I,m,p]of i){if(p.issues){let c={type:"object",origin:"value",input:o,key:I,value:m};for(let T of p.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=p.issues),u.abortEarly){s.typed=!1;break}}p.typed||(s.typed=!1),(p.value!==void 0||I in o)&&(s.value[I]=p.value)}if(!s.issues||!u.abortEarly)for(let[I,m,p]of a){if(p.issues){let c={type:"object",origin:"value",input:o,key:I,value:m};for(let T of p.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=p.issues),u.abortEarly){s.typed=!1;break}}p.typed||(s.typed=!1),s.value[I]=p.value}}else r(this,"type",s,u);return s}}}function j(t,...n){let e={kind:"schema",type:"optional",reference:j,expects:`${t.expects} | undefined`,async:!1,wrapped:t,_run(s,u){return s.value===void 0&&("default"in this&&(s.value=d(this,s,u)),s.value===void 0)?(s.typed=!0,s):this.wrapped._run(s,u)}};return 0 in n&&(e.default=n[0]),e}function v(t,...n){let e={kind:"schema",type:"optional",reference:v,expects:`${t.expects} | undefined`,async:!0,wrapped:t,async _run(s,u){return s.value===void 0&&("default"in this&&(s.value=await d(this,s,u)),s.value===void 0)?(s.typed=!0,s):this.wrapped._run(s,u)}};return 0 in n&&(e.default=n[0]),e}function R(t,n){return{kind:"schema",type:"picklist",reference:R,expects:t.map(f).join(" | ")||"never",async:!1,options:t,message:n,_run(e,s){return this.options.includes(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function qt(t){return{kind:"schema",type:"promise",reference:qt,expects:"Promise",async:!1,message:t,_run(n,e){return n.value instanceof Promise?n.typed=!0:r(this,"type",n,e),n}}}function Dt(t,n,e){return{kind:"schema",type:"record",reference:Dt,expects:"Object",async:!1,key:t,value:n,message:e,_run(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let i in o)if(y(o,i)){let a=o[i],I=this.key._run({typed:!1,value:i},u);if(I.issues){let p={type:"object",origin:"key",input:o,key:i,value:a};for(let c of I.issues)c.path=[p],s.issues?.push(c);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}let m=this.value._run({typed:!1,value:a},u);if(m.issues){let p={type:"object",origin:"value",input:o,key:i,value:a};for(let c of m.issues)c.path?c.path.unshift(p):c.path=[p],s.issues?.push(c);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!I.typed||!m.typed)&&(s.typed=!1),I.typed&&(s.value[I.value]=m.value)}}else r(this,"type",s,u);return s}}}function Nt(t,n,e){return{kind:"schema",type:"record",reference:Nt,expects:"Object",async:!0,key:t,value:n,message:e,async _run(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let i=await Promise.all(Object.entries(o).filter(([a])=>y(o,a)).map(([a,I])=>Promise.all([a,I,this.key._run({typed:!1,value:a},u),this.value._run({typed:!1,value:I},u)])));for(let[a,I,m,p]of i){if(m.issues){let c={type:"object",origin:"key",input:o,key:a,value:I};for(let T of m.issues)T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(p.issues){let c={type:"object",origin:"value",input:o,key:a,value:I};for(let T of p.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=p.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!p.typed)&&(s.typed=!1),m.typed&&(s.value[m.value]=p.value)}}else r(this,"type",s,u);return s}}}function Wt(t,n){return{kind:"schema",type:"set",reference:Wt,expects:"Set",async:!1,value:t,message:n,_run(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;for(let o of u){let i=this.value._run({typed:!1,value:o},s);if(i.issues){let a={type:"set",origin:"value",input:u,key:null,value:o};for(let I of i.issues)I.path?I.path.unshift(a):I.path=[a],e.issues?.push(I);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.add(i.value)}}else r(this,"type",e,s);return e}}}function Vt(t,n){return{kind:"schema",type:"set",reference:Vt,expects:"Set",async:!0,value:t,message:n,async _run(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;let o=await Promise.all([...u].map(async i=>[i,await this.value._run({typed:!1,value:i},s)]));for(let[i,a]of o){if(a.issues){let I={type:"set",origin:"value",input:u,key:null,value:i};for(let m of a.issues)m.path?m.path.unshift(I):m.path=[I],e.issues?.push(m);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.add(a.value)}}else r(this,"type",e,s);return e}}}function Ct(t,n){return{kind:"schema",type:"strict_object",reference:Ct,expects:"Object",async:!1,entries:t,message:n,_run(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let i=u[o],a=this.entries[o]._run({typed:!1,value:i},s);if(a.issues){let I={type:"object",origin:"value",input:u,key:o,value:i};for(let m of a.issues)m.path?m.path.unshift(I):m.path=[I],e.issues?.push(m);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),(a.value!==void 0||o in u)&&(e.value[o]=a.value)}if(!e.issues||!s.abortEarly){for(let o in u)if(!(o in this.entries)){let i=u[o];r(this,"type",e,s,{input:i,expected:"never",path:[{type:"object",origin:"value",input:u,key:o,value:i}]});break}}}else r(this,"type",e,s);return e}}}function Lt(t,n){return{kind:"schema",type:"strict_object",reference:Lt,expects:"Object",async:!0,entries:t,message:n,async _run(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([i,a])=>{let I=u[i];return[i,I,await a._run({typed:!1,value:I},s)]}));for(let[i,a,I]of o){if(I.issues){let m={type:"object",origin:"value",input:u,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],e.issues?.push(p);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),(I.value!==void 0||i in u)&&(e.value[i]=I.value)}if(!e.issues||!s.abortEarly){for(let i in u)if(!(i in this.entries)){let a=u[i];r(this,"type",e,s,{input:a,expected:"never",path:[{type:"object",origin:"value",input:u,key:i,value:a}]});break}}}else r(this,"type",e,s);return e}}}function Kt(t,n){return{kind:"schema",type:"strict_tuple",reference:Kt,expects:"Array",async:!1,items:t,message:n,_run(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let i=u[o],a=this.items[o]._run({typed:!1,value:i},s);if(a.issues){let I={type:"array",origin:"value",input:u,key:o,value:i};for(let m of a.issues)m.path?m.path.unshift(I):m.path=[I],e.issues?.push(m);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.push(a.value)}if(!(e.issues&&s.abortEarly)&&this.items.length<u.length){let o=u[t.length];r(this,"type",e,s,{input:o,expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:o}]})}}else r(this,"type",e,s);return e}}}function $t(t,n){return{kind:"schema",type:"strict_tuple",reference:$t,expects:"Array",async:!0,items:t,message:n,async _run(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(i,a)=>{let I=u[a];return[a,I,await i._run({typed:!1,value:I},s)]}));for(let[i,a,I]of o){if(I.issues){let m={type:"array",origin:"value",input:u,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],e.issues?.push(p);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}if(!(e.issues&&s.abortEarly)&&this.items.length<u.length){let i=u[t.length];r(this,"type",e,s,{input:i,expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:i}]})}}else r(this,"type",e,s);return e}}}function zt(t){return{kind:"schema",type:"string",reference:zt,expects:"string",async:!1,message:t,_run(n,e){return typeof n.value=="string"?n.typed=!0:r(this,"type",n,e),n}}}function Ft(t){return{kind:"schema",type:"symbol",reference:Ft,expects:"symbol",async:!1,message:t,_run(n,e){return typeof n.value=="symbol"?n.typed=!0:r(this,"type",n,e),n}}}function Ut(t,n){return{kind:"schema",type:"tuple",reference:Ut,expects:"Array",async:!1,items:t,message:n,_run(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let i=u[o],a=this.items[o]._run({typed:!1,value:i},s);if(a.issues){let I={type:"array",origin:"value",input:u,key:o,value:i};for(let m of a.issues)m.path?m.path.unshift(I):m.path=[I],e.issues?.push(m);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.push(a.value)}}else r(this,"type",e,s);return e}}}function Gt(t,n){return{kind:"schema",type:"tuple",reference:Gt,expects:"Array",async:!0,items:t,message:n,async _run(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(i,a)=>{let I=u[a];return[a,I,await i._run({typed:!1,value:I},s)]}));for(let[i,a,I]of o){if(I.issues){let m={type:"array",origin:"value",input:u,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],e.issues?.push(p);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Xt(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:Xt,expects:"Array",async:!1,items:t,rest:n,message:e,_run(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];for(let i=0;i<this.items.length;i++){let a=o[i],I=this.items[i]._run({typed:!1,value:a},u);if(I.issues){let m={type:"array",origin:"value",input:o,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],s.issues?.push(p);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}I.typed||(s.typed=!1),s.value.push(I.value)}if(!s.issues||!u.abortEarly)for(let i=this.items.length;i<o.length;i++){let a=o[i],I=this.rest._run({typed:!1,value:a},u);if(I.issues){let m={type:"array",origin:"value",input:o,key:i,value:a};for(let p of I.issues)p.path?p.path.unshift(m):p.path=[m],s.issues?.push(p);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}I.typed||(s.typed=!1),s.value.push(I.value)}}else r(this,"type",s,u);return s}}}function Ht(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:Ht,expects:"Array",async:!0,items:t,rest:n,message:e,async _run(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];let[i,a]=await Promise.all([Promise.all(this.items.map(async(I,m)=>{let p=o[m];return[m,p,await I._run({typed:!1,value:p},u)]})),Promise.all(o.slice(this.items.length).map(async(I,m)=>[m+this.items.length,I,await this.rest._run({typed:!1,value:I},u)]))]);for(let[I,m,p]of i){if(p.issues){let c={type:"array",origin:"value",input:o,key:I,value:m};for(let T of p.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=p.issues),u.abortEarly){s.typed=!1;break}}p.typed||(s.typed=!1),s.value.push(p.value)}if(!s.issues||!u.abortEarly)for(let[I,m,p]of a){if(p.issues){let c={type:"array",origin:"value",input:o,key:I,value:m};for(let T of p.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=p.issues),u.abortEarly){s.typed=!1;break}}p.typed||(s.typed=!1),s.value.push(p.value)}}else r(this,"type",s,u);return s}}}function Jt(t){return{kind:"schema",type:"undefined",reference:Jt,expects:"undefined",async:!1,message:t,_run(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function g(t){let n;if(t)for(let e of t)n?n.push(...e.issues):n=e.issues;return n}function Zt(t,n){return{kind:"schema",type:"union",reference:Zt,expects:[...new Set(t.map(e=>e.expects))].join(" | ")||"never",async:!1,options:t,message:n,_run(e,s){let u,o,i;for(let a of this.options){let I=a._run({typed:!1,value:e.value},s);if(I.typed)if(I.issues)o?o.push(I):o=[I];else{u=I;break}else i?i.push(I):i=[I]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:g(o)}),e.typed=!0}else{if(i?.length===1)return i[0];r(this,"type",e,s,{issues:g(i)})}return e}}}function Qt(t,n){return{kind:"schema",type:"union",reference:Qt,expects:[...new Set(t.map(e=>e.expects))].join(" | ")||"never",async:!0,options:t,message:n,async _run(e,s){let u,o,i;for(let a of this.options){let I=await a._run({typed:!1,value:e.value},s);if(I.typed)if(I.issues)o?o.push(I):o=[I];else{u=I;break}else i?i.push(I):i=[I]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:g(o)}),e.typed=!0}else{if(i?.length===1)return i[0];r(this,"type",e,s,{issues:g(i)})}return e}}}function Yt(){return{kind:"schema",type:"unknown",reference:Yt,expects:"unknown",async:!1,_run(t){return t.typed=!0,t}}}function b(t,n,e=new Set){for(let s of n)s.type==="variant"?b(t,s.options,e):e.add(s.entries[t].expects);return e}function es(t,n,e){let s;return{kind:"schema",type:"variant",reference:es,expects:"Object",async:!1,key:t,options:n,message:e,_run(u,o){let i=u.value;if(i&&typeof i=="object"){let a=i[this.key];if(this.key in i){let I;for(let m of this.options)if(m.type==="variant"||!m.entries[this.key]._run({typed:!1,value:a},o).issues){let p=m._run({typed:!1,value:i},o);if(!p.issues)return p;(!I||!I.typed&&p.typed)&&(I=p)}if(I)return I}s||(s=[...b(this.key,this.options)].join(" | ")||"never"),r(this,"type",u,o,{input:a,expected:s,path:[{type:"object",origin:"value",input:i,key:this.key,value:a}]})}else r(this,"type",u,o);return u}}}function ns(t,n,e){let s;return{kind:"schema",type:"variant",reference:ns,expects:"Object",async:!0,key:t,options:n,message:e,async _run(u,o){let i=u.value;if(i&&typeof i=="object"){let a=i[this.key];if(this.key in i){let I;for(let m of this.options)if(m.type==="variant"||!(await m.entries[this.key]._run({typed:!1,value:a},o)).issues){let p=await m._run({typed:!1,value:i},o);if(!p.issues)return p;(!I||!I.typed&&p.typed)&&(I=p)}if(I)return I}s||(s=[...b(this.key,this.options)].join(" | ")||"never"),r(this,"type",u,o,{input:a,expected:s,path:[{type:"object",origin:"value",input:i,key:this.key,value:a}]})}else r(this,"type",u,o);return u}}}function ts(t){return{kind:"schema",type:"void",reference:ts,expects:"void",async:!1,message:t,_run(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function Rs(t,n){return R(Object.keys(t.entries),n)}function _s(t,n){let e={...t.entries};for(let s of n)delete e[s];return{...t,entries:e}}function Te(t,n,e){let s=t._run({typed:!1,value:n},l(e));if(s.issues)throw new k(s.issues);return s.value}async function fe(t,n,e){let s=await t._run({typed:!1,value:n},l(e));if(s.issues)throw new k(s.issues);return s.value}function qs(t,n){let e=s=>Te(t,s,n);return e.schema=t,e.config=n,e}function Ds(t,n){let e=s=>fe(t,s,n);return e.schema=t,e.config=n,e}function Ns(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?j(t.entries[s]):t.entries[s];return{...t,entries:e}}function Ws(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?v(t.entries[s]):t.entries[s];return{...t,entries:e}}function Vs(t,n){let e={};for(let s of n)e[s]=t.entries[s];return{...t,entries:e}}function Cs(...t){return{...t[0],pipe:t,_run(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=s._run(n,e))}return n}}}function Ls(...t){return{...t[0],pipe:t,async:!0,async _run(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=await s._run(n,e))}return n}}}function Ks(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let i in t.entries)o[i]=!s||s.includes(i)?M(t.entries[i],u):t.entries[i];return{...t,entries:o}}function $s(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let i in t.entries)o[i]=!s||s.includes(i)?P(t.entries[i],u):t.entries[i];return{...t,entries:o}}function de(t,n,e){let s=t._run({typed:!1,value:n},l(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}async function ye(t,n,e){let s=await t._run({typed:!1,value:n},l(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}function zs(t,n){let e=s=>de(t,s,n);return e.schema=t,e.config=n,e}function Fs(t,n){let e=s=>ye(t,s,n);return e.schema=t,e.config=n,e}function Us(t){return t.wrapped}0&&(module.exports={BASE64_REGEX,BIC_REGEX,CUID2_REGEX,DECIMAL_REGEX,EMAIL_REGEX,EMOJI_REGEX,HEXADECIMAL_REGEX,HEX_COLOR_REGEX,IMEI_REGEX,IPV4_REGEX,IPV6_REGEX,IP_REGEX,ISO_DATE_REGEX,ISO_DATE_TIME_REGEX,ISO_TIMESTAMP_REGEX,ISO_TIME_REGEX,ISO_TIME_SECOND_REGEX,ISO_WEEK_REGEX,MAC48_REGEX,MAC64_REGEX,MAC_REGEX,OCTAL_REGEX,ULID_REGEX,UUID_REGEX,ValiError,_addIssue,_isLuhnAlgo,_isValidObjectKey,_stringify,any,array,arrayAsync,awaitAsync,base64,bic,bigint,blob,boolean,brand,bytes,check,checkAsync,checkItems,config,creditCard,cuid2,custom,customAsync,date,decimal,deleteGlobalConfig,deleteGlobalMessage,deleteSchemaMessage,deleteSpecificMessage,description,email,emoji,empty,endsWith,entriesFromList,enum:null,enum_,everyItem,excludes,fallback,fallbackAsync,file,filterItems,findItem,finite,flatten,forward,forwardAsync,function:null,function_,getDefault,getDefaults,getDefaultsAsync,getDotPath,getFallback,getFallbacks,getFallbacksAsync,getGlobalConfig,getGlobalMessage,getSchemaMessage,getSpecificMessage,hash,hexColor,hexadecimal,imei,includes,instance,integer,intersect,intersectAsync,ip,ipv4,ipv6,is,isOfKind,isOfType,isValiError,isoDate,isoDateTime,isoTime,isoTimeSecond,isoTimestamp,isoWeek,keyof,lazy,lazyAsync,length,literal,looseObject,looseObjectAsync,looseTuple,looseTupleAsync,mac,mac48,mac64,map,mapAsync,mapItems,maxBytes,maxLength,maxSize,maxValue,mimeType,minBytes,minLength,minSize,minValue,multipleOf,nan,never,nonEmpty,nonNullable,nonNullableAsync,nonNullish,nonNullishAsync,nonOptional,nonOptionalAsync,normalize,notBytes,notLength,notSize,notValue,null:null,null_,nullable,nullableAsync,nullish,nullishAsync,number,object,objectAsync,objectWithRest,objectWithRestAsync,octal,omit,optional,optionalAsync,parse,parseAsync,parser,parserAsync,partial,partialAsync,partialCheck,partialCheckAsync,pick,picklist,pipe,pipeAsync,promise,rawCheck,rawCheckAsync,rawTransform,rawTransformAsync,readonly,record,recordAsync,reduceItems,regex,required,requiredAsync,safeInteger,safeParse,safeParseAsync,safeParser,safeParserAsync,set,setAsync,setGlobalConfig,setGlobalMessage,setSchemaMessage,setSpecificMessage,size,someItem,sortItems,startsWith,strictObject,strictObjectAsync,strictTuple,strictTupleAsync,string,symbol,toLowerCase,toMaxValue,toMinValue,toUpperCase,transform,transformAsync,trim,trimEnd,trimStart,tuple,tupleAsync,tupleWithRest,tupleWithRestAsync,ulid,undefined,undefined_,union,unionAsync,unknown,unwrap,url,uuid,value,variant,variantAsync,void:null,void_});
