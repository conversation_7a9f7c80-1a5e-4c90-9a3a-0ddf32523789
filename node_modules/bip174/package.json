{"name": "bip174", "version": "2.1.1", "description": "", "main": "src/lib/psbt.js", "types": "src/lib/psbt.d.ts", "files": ["src/lib"], "engines": {"node": ">=8.0.0"}, "scripts": {"build": "npm run clean && tsc -p ./tsconfig.json && rimraf 'src/tests/**/*.d.ts' && npm run formatjs", "clean": "rimraf src types", "coverage": "nyc --check-coverage --exclude='src/tests/**/*.js' --reporter=lcov --reporter=text --branches 90 --functions 90 --lines 90 npm run unit", "flb": "npm run format && npm run lint && npm run build", "format": "npm run prettier -- --write", "formatjs": "npm run prettierjs -- --write > /dev/null 2>&1", "format:ci": "npm run prettier -- --check && npm run prettierjs -- --check", "gitdiff:ci": "npm run build && git diff --exit-code", "lint": "tslint -p tsconfig.json -c tslint.json", "prettier": "prettier 'ts_src/**/*.ts' --ignore-path ./.prettierignore", "prettierjs": "prettier 'src/**/*.js' --ignore-path ./.prettierignore", "test": "npm run format:ci && npm run lint && npm run build && npm run coverage", "unit": "tape src/tests/*.js src/tests/fromBIP/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/bitcoinjs/bip174.git"}, "devDependencies": {"@types/node": "12.0.8", "@types/tape": "4.2.33", "bitcoinjs-lib": "^5.0.5", "nyc": "^14.1.1", "prettier": "^1.18.2", "rimraf": "^2.6.3", "tape": "^4.10.2", "tslint": "5.17.0", "typescript": "3.5.2"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/bitcoinjs/bip174/issues"}, "homepage": "https://github.com/bitcoinjs/bip174#readme"}