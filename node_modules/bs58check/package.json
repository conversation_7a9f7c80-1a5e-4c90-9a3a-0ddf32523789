{"name": "bs58check", "version": "3.0.1", "description": "A straightforward implementation of base58-check encoding", "keywords": ["base", "base58", "base58check", "bitcoin", "bs58", "check", "checksum", "decode", "decoding", "encode", "encoding", "litecoin"], "homepage": "https://github.com/bitcoinjs/bs58check", "bugs": {"url": "https://github.com/bitcoinjs/bs58check/issues"}, "license": "MIT", "author": "<PERSON>", "files": ["index.js", "index.d.ts", "base.js"], "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/bitcoinjs/bs58check.git"}, "scripts": {"coverage-report": "nyc report --reporter=lcov", "coverage": "nyc --check-coverage --branches 90 --functions 90 npm run unit", "standard": "standard", "test": "npm run standard && npm run coverage", "unit": "tape test/*.js"}, "dependencies": {"@noble/hashes": "^1.2.0", "bs58": "^5.0.0"}, "devDependencies": {"blake-hash": "^1.0.0", "nyc": "^15.0.0", "safe-buffer": "^5.1.2", "standard": "^14.3.3", "tape": "^4.13.2"}}