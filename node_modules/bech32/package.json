{"name": "bech32", "version": "2.0.0", "description": "Bech32 encoding / decoding", "keywords": ["base32", "bech32", "bech32m", "bitcoin", "crypto", "crytography", "decode", "decoding", "encode", "encoding"], "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/index.js", "dist/index.d.ts"], "license": "MIT", "devDependencies": {"@types/node": "^14.0.14", "@types/tape": "^4.13.0", "nyc": "^15.0.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "tap-dot": "*", "tape": "^4.13.2", "tslint": "^6.1.3", "typescript": "^3.9.5"}, "repository": {"url": "http://github.com/bitcoinjs/bech32", "type": "git"}, "scripts": {"build": "npm run clean && tsc -d", "clean": "<PERSON><PERSON><PERSON> dist", "coverage": "nyc -x dist/test/*.js --check-coverage --branches 90 --functions 90 tape dist/test/*.js", "format": "npm run prettier -- --write", "format:ci": "npm run prettier -- --check", "gitdiff:ci": "npm run build && git diff --exit-code", "lint": "tslint -p tsconfig.json -c tslint.json", "prettier": "prettier --print-width 100 --single-quote --trailing-comma=all \"**/!(*.d).ts\"", "test": "tape dist/test/*.js | tap-dot"}}