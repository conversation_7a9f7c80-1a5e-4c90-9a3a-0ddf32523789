const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { Worker } = require('worker_threads');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

const PORT = process.env.PORT || 3001;

// Serve static files
app.use(express.static('public'));
app.use(express.json());

// Store active workers
const activeWorkers = new Map();

// Socket.io connection handling
io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);

    socket.on('start-generation', (data) => {
        const { walletCount, cryptoType, isInfinite } = data;
        
        // Create worker thread
        const worker = new Worker('./wallet-worker.js', {
            workerData: {
                walletCount: isInfinite ? -1 : walletCount,
                cryptoType,
                socketId: socket.id
            }
        });

        // Store worker reference
        activeWorkers.set(socket.id, worker);

        // Handle messages from worker
        worker.on('message', (message) => {
            socket.emit('wallet-result', message);
        });

        // Handle worker errors
        worker.on('error', (error) => {
            console.error('Worker error:', error);
            socket.emit('error', { message: error.message });
        });

        // Handle worker exit
        worker.on('exit', (code) => {
            console.log(`Worker exited with code ${code}`);
            activeWorkers.delete(socket.id);
            socket.emit('generation-complete');
        });

        socket.emit('generation-started');
    });

    socket.on('stop-generation', () => {
        const worker = activeWorkers.get(socket.id);
        if (worker) {
            worker.terminate();
            activeWorkers.delete(socket.id);
            socket.emit('generation-stopped');
        }
    });

    socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
        const worker = activeWorkers.get(socket.id);
        if (worker) {
            worker.terminate();
            activeWorkers.delete(socket.id);
        }
    });
});

// API Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/api/status', (req, res) => {
    res.json({
        activeWorkers: activeWorkers.size,
        uptime: process.uptime()
    });
});

server.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});
