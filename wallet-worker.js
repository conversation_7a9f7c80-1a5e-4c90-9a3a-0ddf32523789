const { parentPort, workerData } = require('worker_threads');
const bitcoin = require('bitcoinjs-lib');
const ecc = require('tiny-secp256k1');
const { ECPairFactory } = require('ecpair');
const { ethers } = require('ethers');
const axios = require('axios');
const crypto = require('crypto');

// Initialize ECPair with tiny-secp256k1
const ECPair = ECPairFactory(ecc);

// Initialize bitcoin library
bitcoin.initEccLib(ecc);

const { walletCount, cryptoType, socketId } = workerData;

// API endpoints for balance checking
const API_ENDPOINTS = {
    bitcoin: 'https://blockstream.info/api/address/',
    ethereum: 'https://api.etherscan.io/api'
};

// Rate limiting
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
let lastRequestTime = 0;

async function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function generateBitcoinWallet() {
    try {
        // Generate random private key
        const privateKey = crypto.randomBytes(32);
        console.log('Generated private key:', privateKey.toString('hex'));

        const keyPair = ECPair.fromPrivateKey(privateKey);
        console.log('Generated keyPair:', !!keyPair);
        console.log('Public key:', keyPair.publicKey.toString('hex'));

        // Generate address using the keyPair directly
        const payment = bitcoin.payments.p2pkh({
            pubkey: keyPair.publicKey,
            network: bitcoin.networks.bitcoin
        });

        console.log('Generated payment:', payment);

        return {
            address: payment.address,
            privateKey: keyPair.toWIF(),
            type: 'Bitcoin'
        };
    } catch (error) {
        console.error('Bitcoin wallet generation error:', error);
        throw new Error(`Bitcoin wallet generation failed: ${error.message}`);
    }
}

async function generateEthereumWallet() {
    try {
        // Generate random wallet
        const wallet = ethers.Wallet.createRandom();
        
        return {
            address: wallet.address,
            privateKey: wallet.privateKey,
            type: 'Ethereum'
        };
    } catch (error) {
        throw new Error(`Ethereum wallet generation failed: ${error.message}`);
    }
}

async function checkBitcoinBalance(address) {
    try {
        // Rate limiting
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        const response = await axios.get(`${API_ENDPOINTS.bitcoin}${address}`, {
            timeout: 10000
        });
        
        // Balance is in satoshis, convert to BTC
        const balanceSatoshis = response.data.chain_stats.funded_txo_sum - response.data.chain_stats.spent_txo_sum;
        return balanceSatoshis / 100000000; // Convert to BTC
    } catch (error) {
        if (error.response && error.response.status === 404) {
            return 0; // Address not found means 0 balance
        }
        throw new Error(`Bitcoin balance check failed: ${error.message}`);
    }
}

async function checkEthereumBalance(address) {
    try {
        // Rate limiting
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        // Using free Etherscan API (limited requests)
        const response = await axios.get(API_ENDPOINTS.ethereum, {
            params: {
                module: 'account',
                action: 'balance',
                address: address,
                tag: 'latest',
                apikey: 'YourApiKeyToken' // You should get a free API key from Etherscan
            },
            timeout: 10000
        });

        if (response.data.status === '1') {
            // Balance is in wei, convert to ETH
            return parseFloat(ethers.formatEther(response.data.result));
        } else {
            return 0;
        }
    } catch (error) {
        throw new Error(`Ethereum balance check failed: ${error.message}`);
    }
}

async function generateAndCheckWallet(type) {
    try {
        let wallet;
        let balance = 0;

        // Generate wallet
        if (type === 'bitcoin') {
            wallet = await generateBitcoinWallet();
            balance = await checkBitcoinBalance(wallet.address);
        } else if (type === 'ethereum') {
            wallet = await generateEthereumWallet();
            balance = await checkEthereumBalance(wallet.address);
        } else {
            throw new Error(`Unsupported crypto type: ${type}`);
        }

        const result = {
            ...wallet,
            balance,
            timestamp: new Date().toISOString()
        };

        return result;
    } catch (error) {
        return {
            address: 'ERROR',
            privateKey: 'ERROR',
            type: type || 'Unknown',
            balance: 0,
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

async function main() {
    let processed = 0;
    const isInfinite = walletCount === -1;

    parentPort.postMessage({
        type: 'status',
        message: `Starting ${isInfinite ? 'infinite' : walletCount} ${cryptoType} wallet generation...`
    });

    while (isInfinite || processed < walletCount) {
        try {
            const result = await generateAndCheckWallet(cryptoType);
            
            parentPort.postMessage({
                type: 'wallet',
                data: result,
                processed: processed + 1,
                total: isInfinite ? 'infinite' : walletCount
            });

            processed++;

            // Small delay to prevent overwhelming the APIs
            await delay(100);

        } catch (error) {
            parentPort.postMessage({
                type: 'error',
                message: error.message
            });
        }
    }

    parentPort.postMessage({
        type: 'complete',
        message: `Completed processing ${processed} wallets`
    });
}

// Start the worker
main().catch(error => {
    parentPort.postMessage({
        type: 'error',
        message: error.message
    });
});
