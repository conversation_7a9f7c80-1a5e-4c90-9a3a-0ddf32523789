# Crypto Wallet Generator

A web-based application that randomly generates cryptocurrency wallets and checks their balances using multi-threading for optimal performance.

## Features

- **Multi-cryptocurrency support**: Bitcoin and Ethereum
- **Multi-threading**: Uses Node.js worker threads for parallel processing
- **Real-time updates**: WebSocket-based live updates
- **Infinite mode**: Generate wallets continuously until stopped
- **Balance checking**: Automatically checks wallet balances using public APIs
- **Statistics**: Track generation speed, success rate, and total balances found
- **Responsive UI**: Modern, mobile-friendly interface

## Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd /Users/<USER>/Projects/crypto-test
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the server**
   ```bash
   npm start
   ```

4. **Open your browser**
   Navigate to `http://localhost:3001`

## Usage

1. **Select cryptocurrency type** (Bitcoin or Ethereum)
2. **Choose number of wallets** to generate (1-1000) or enable infinite mode
3. **Click "Start Generation"** to begin
4. **Monitor progress** in real-time
5. **Stop anytime** using the Stop button
6. **Clear results** to start fresh

## API Endpoints Used

- **Bitcoin**: Blockstream API (`https://blockstream.info/api/`)
- **Ethereum**: Etherscan API (`https://api.etherscan.io/api`)

## Configuration

### Etherscan API Key
For better Ethereum balance checking performance, get a free API key from [Etherscan.io](https://etherscan.io/apis) and replace `'YourApiKeyToken'` in `wallet-worker.js`.

### Rate Limiting
The application includes built-in rate limiting (1 second between API calls) to respect API limits. You can adjust this in `wallet-worker.js`:

```javascript
const RATE_LIMIT_DELAY = 1000; // milliseconds
```

## Technical Details

### Architecture
- **Backend**: Node.js + Express + Socket.IO
- **Frontend**: Vanilla HTML/CSS/JavaScript
- **Multi-threading**: Node.js Worker Threads
- **Real-time communication**: WebSocket (Socket.IO)

### Security Notes
- Private keys are generated randomly using cryptographically secure methods
- No private keys are stored or logged
- All wallet generation happens locally
- Balance checking uses public APIs only

### Performance
- Uses worker threads to prevent blocking the main thread
- Rate limiting prevents API abuse
- Efficient memory management for large result sets
- Progressive loading of results in the UI

## Development

### Start in development mode
```bash
npm run dev
```

This uses nodemon for automatic server restarts on file changes.

### Project Structure
```
├── server.js              # Main Express server
├── wallet-worker.js       # Worker thread for wallet generation
├── package.json           # Dependencies and scripts
├── public/
│   ├── index.html         # Main HTML page
│   ├── style.css          # Styling
│   └── script.js          # Frontend JavaScript
└── README.md              # This file
```

## Disclaimer

This tool is for educational and research purposes only. The probability of finding a wallet with a balance is extremely low (practically zero). Do not use this for any illegal activities. Always respect API rate limits and terms of service.

## License

MIT License - feel free to modify and distribute.
