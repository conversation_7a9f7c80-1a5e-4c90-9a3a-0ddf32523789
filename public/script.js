// Initialize Socket.IO connection
const socket = io();

// DOM elements
const startBtn = document.getElementById('startBtn');
const stopBtn = document.getElementById('stopBtn');
const clearBtn = document.getElementById('clearBtn');
const cryptoTypeSelect = document.getElementById('cryptoType');
const walletCountInput = document.getElementById('walletCount');
const infiniteModeCheckbox = document.getElementById('infiniteMode');
const statusSpan = document.getElementById('status');
const processedSpan = document.getElementById('processed');
const foundWithBalanceSpan = document.getElementById('foundWithBalance');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const resultsList = document.getElementById('resultsList');

// Statistics elements
const totalGeneratedSpan = document.getElementById('totalGenerated');
const totalWithBalanceSpan = document.getElementById('totalWithBalance');
const totalBalanceSpan = document.getElementById('totalBalance');
const avgTimeSpan = document.getElementById('avgTime');

// State variables
let isRunning = false;
let startTime = null;
let processedCount = 0;
let foundWithBalanceCount = 0;
let totalBalance = 0;
let results = [];

// Event listeners
startBtn.addEventListener('click', startGeneration);
stopBtn.addEventListener('click', stopGeneration);
clearBtn.addEventListener('click', clearResults);
infiniteModeCheckbox.addEventListener('change', toggleInfiniteMode);

// Socket event listeners
socket.on('generation-started', () => {
    isRunning = true;
    startTime = Date.now();
    updateUI();
    statusSpan.textContent = 'Generating wallets...';
});

socket.on('generation-stopped', () => {
    isRunning = false;
    updateUI();
    statusSpan.textContent = 'Stopped';
});

socket.on('generation-complete', () => {
    isRunning = false;
    updateUI();
    statusSpan.textContent = 'Complete';
});

socket.on('wallet-result', (message) => {
    handleWalletResult(message);
});

socket.on('error', (error) => {
    console.error('Error:', error);
    statusSpan.textContent = `Error: ${error.message}`;
    isRunning = false;
    updateUI();
});

function startGeneration() {
    const cryptoType = cryptoTypeSelect.value;
    const walletCount = parseInt(walletCountInput.value);
    const isInfinite = infiniteModeCheckbox.checked;

    if (!isInfinite && (!walletCount || walletCount < 1)) {
        alert('Please enter a valid number of wallets');
        return;
    }

    // Reset counters
    processedCount = 0;
    foundWithBalanceCount = 0;
    totalBalance = 0;
    results = [];

    socket.emit('start-generation', {
        walletCount,
        cryptoType,
        isInfinite
    });
}

function stopGeneration() {
    socket.emit('stop-generation');
}

function clearResults() {
    resultsList.innerHTML = '';
    results = [];
    processedCount = 0;
    foundWithBalanceCount = 0;
    totalBalance = 0;
    updateStatistics();
    updateProgress(0, infiniteModeCheckbox.checked ? 'infinite' : parseInt(walletCountInput.value));
    statusSpan.textContent = 'Ready';
}

function toggleInfiniteMode() {
    walletCountInput.disabled = infiniteModeCheckbox.checked;
    if (infiniteModeCheckbox.checked) {
        walletCountInput.placeholder = 'Infinite mode enabled';
    } else {
        walletCountInput.placeholder = 'Enter number of wallets';
    }
}

function handleWalletResult(message) {
    if (message.type === 'wallet') {
        const walletData = message.data;
        processedCount = message.processed;

        // Add to results
        results.push(walletData);

        // Update counters
        if (walletData.balance && walletData.balance > 0) {
            foundWithBalanceCount++;
            totalBalance += walletData.balance;
        }

        // Update UI
        addResultToList(walletData);
        updateProgress(message.processed, message.total);
        updateStatistics();

        processedSpan.textContent = message.processed;
        foundWithBalanceSpan.textContent = foundWithBalanceCount;
    } else if (message.type === 'status') {
        statusSpan.textContent = message.message;
    } else if (message.type === 'error') {
        console.error('Worker error:', message.message);
    }
}

function addResultToList(walletData) {
    const resultItem = document.createElement('div');
    resultItem.className = 'result-item';
    
    if (walletData.balance && walletData.balance > 0) {
        resultItem.classList.add('has-balance');
    }

    const hasBalance = walletData.balance && walletData.balance > 0;
    const balanceClass = hasBalance ? 'balance' : 'balance zero';
    const balanceText = hasBalance ? 
        `${walletData.balance.toFixed(8)} ${walletData.type === 'Bitcoin' ? 'BTC' : 'ETH'}` : 
        '0';

    resultItem.innerHTML = `
        <div class="address" title="${walletData.address || 'N/A'}">${walletData.address || 'N/A'}</div>
        <div class="private-key" title="${walletData.privateKey || 'N/A'}">${walletData.privateKey || 'N/A'}</div>
        <div class="${balanceClass}">${balanceText}</div>
        <div>${walletData.type || 'Unknown'}</div>
        <div>${new Date(walletData.timestamp).toLocaleTimeString()}</div>
    `;

    // Insert at the top for latest results
    resultsList.insertBefore(resultItem, resultsList.firstChild);

    // Limit displayed results to prevent performance issues
    const maxDisplayedResults = 100;
    while (resultsList.children.length > maxDisplayedResults) {
        resultsList.removeChild(resultsList.lastChild);
    }
}

function updateProgress(processed, total) {
    if (total === 'infinite') {
        progressFill.style.width = '100%';
        progressText.textContent = `${processed} wallets processed`;
    } else {
        const percentage = (processed / total) * 100;
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${processed}/${total} (${percentage.toFixed(1)}%)`;
    }
}

function updateStatistics() {
    totalGeneratedSpan.textContent = processedCount;
    totalWithBalanceSpan.textContent = foundWithBalanceCount;
    
    const cryptoSymbol = cryptoTypeSelect.value === 'bitcoin' ? 'BTC' : 'ETH';
    totalBalanceSpan.textContent = `${totalBalance.toFixed(8)} ${cryptoSymbol}`;
    
    if (startTime && processedCount > 0) {
        const elapsed = Date.now() - startTime;
        const avgTime = elapsed / processedCount;
        avgTimeSpan.textContent = `${avgTime.toFixed(0)}ms`;
    }
}

function updateUI() {
    startBtn.disabled = isRunning;
    stopBtn.disabled = !isRunning;
    cryptoTypeSelect.disabled = isRunning;
    walletCountInput.disabled = isRunning || infiniteModeCheckbox.checked;
    infiniteModeCheckbox.disabled = isRunning;
}

// Initialize UI
updateUI();
