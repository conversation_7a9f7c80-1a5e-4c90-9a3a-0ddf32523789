<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Wallet Generator</title>
    <link rel="stylesheet" href="style.css">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🔐 Crypto Wallet Generator</h1>
            <p>Generate random crypto wallets and check their balances</p>
        </header>

        <div class="controls">
            <div class="input-group">
                <label for="cryptoType">Cryptocurrency:</label>
                <select id="cryptoType">
                    <option value="bitcoin">Bitcoin (BTC)</option>
                    <option value="ethereum">Ethereum (ETH)</option>
                </select>
            </div>

            <div class="input-group">
                <label for="walletCount">Number of wallets:</label>
                <input type="number" id="walletCount" min="1" max="1000" value="10" placeholder="Enter number of wallets">
            </div>

            <div class="input-group">
                <label>
                    <input type="checkbox" id="infiniteMode"> Infinite mode (run until stopped)
                </label>
            </div>

            <div class="button-group">
                <button id="startBtn" class="btn btn-primary">Start Generation</button>
                <button id="stopBtn" class="btn btn-danger" disabled>Stop</button>
                <button id="clearBtn" class="btn btn-secondary">Clear Results</button>
            </div>
        </div>

        <div class="status">
            <div class="status-item">
                <span class="label">Status:</span>
                <span id="status" class="value">Ready</span>
            </div>
            <div class="status-item">
                <span class="label">Processed:</span>
                <span id="processed" class="value">0</span>
            </div>
            <div class="status-item">
                <span class="label">Found with Balance:</span>
                <span id="foundWithBalance" class="value">0</span>
            </div>
        </div>

        <div class="progress-container">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div id="progressText" class="progress-text">0%</div>
        </div>

        <div class="results">
            <h2>Results</h2>
            <div class="results-header">
                <span>Address</span>
                <span>Private Key</span>
                <span>Balance</span>
                <span>Type</span>
                <span>Time</span>
            </div>
            <div id="resultsList" class="results-list">
                <!-- Results will be populated here -->
            </div>
        </div>

        <div class="stats">
            <h3>Statistics</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalGenerated">0</div>
                    <div class="stat-label">Total Generated</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalWithBalance">0</div>
                    <div class="stat-label">With Balance</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalBalance">0</div>
                    <div class="stat-label">Total Balance</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgTime">0ms</div>
                    <div class="stat-label">Avg Time/Wallet</div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
